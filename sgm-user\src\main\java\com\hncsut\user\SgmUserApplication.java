package com.hncsut.user;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 用户管理服务启动程序
 * 
 * <AUTHOR>
 */
@EnableDiscoveryClient
@SpringBootApplication
public class SgmUserApplication {
    public static void main(String[] args) {
        SpringApplication.run(SgmUserApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  用户管理服务启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
