package com.hncsut.auth.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 登录对象
 * 
 * <AUTHOR>
 */
@Data
public class LoginDto {

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    private String username;

    /**
     * 用户密码
     */
    @NotBlank(message = "用户密码不能为空")
    private String password;

    /**
     * 验证码
     */
    private String code;

    /**
     * 唯一标识
     */
    private String uuid;
}
