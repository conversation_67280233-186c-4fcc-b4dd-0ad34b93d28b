package com.hncsut.file.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hncsut.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文件实体类
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_file")
public class SysFile extends BaseEntity {

    /**
     * 原始文件名
     */
    private String originalName;

    /**
     * 存储文件名
     */
    private String fileName;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 访问URL
     */
    private String fileUrl;

    /**
     * 文件大小(字节)
     */
    private Long fileSize;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * MIME类型
     */
    private String mimeType;

    /**
     * 文件MD5
     */
    private String fileMd5;

    /**
     * 存储类型(1:本地 2:MinIO 3:阿里云OSS)
     */
    private Integer storageType;

    /**
     * 存储桶名称
     */
    private String bucketName;

    /**
     * 是否公开(0:私有 1:公开)
     */
    private Integer isPublic;

    /**
     * 下载次数
     */
    private Integer downloadCount;
}
