import React, { useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { Navigate } from 'react-router-dom'
import { Spin } from 'antd'
import { RootState, AppDispatch } from '@/store'
import { getUserInfo } from '@/store/slices/authSlice'

interface ProtectedRouteProps {
  children: React.ReactNode
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const dispatch = useDispatch<AppDispatch>()
  const { isAuthenticated, user, isLoading } = useSelector((state: RootState) => state.auth)

  useEffect(() => {
    if (isAuthenticated && !user) {
      dispatch(getUserInfo())
    }
  }, [isAuthenticated, user, dispatch])

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />
  }

  if (isLoading || (isAuthenticated && !user)) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Spin size="large" />
      </div>
    )
  }

  return <>{children}</>
}

export default ProtectedRoute
