package com.hncsut.file.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hncsut.file.config.MinioConfig;
import com.hncsut.file.domain.entity.SysFile;
import com.hncsut.file.mapper.SysFileMapper;
import com.hncsut.file.service.FileService;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.RemoveObjectArgs;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.http.Method;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 文件服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileServiceImpl implements FileService {

    private final MinioClient minioClient;
    private final MinioConfig minioConfig;
    private final SysFileMapper sysFileMapper;

    @Override
    public Map<String, Object> uploadFile(MultipartFile file) {
        try {
            // 计算文件MD5
            String md5 = DigestUtil.md5Hex(file.getInputStream());
            
            // 检查文件是否已存在
            Map<String, Object> existFile = findByMd5(md5);
            if (existFile != null) {
                return existFile;
            }

            // 生成文件名和路径
            String originalName = file.getOriginalFilename();
            String extension = FileUtil.extName(originalName);
            String fileName = IdUtil.simpleUUID() + "." + extension;
            String datePath = DateUtil.format(new Date(), "yyyy/MM/dd");
            String filePath = datePath + "/" + fileName;

            // 上传到MinIO
            InputStream inputStream = file.getInputStream();
            minioClient.putObject(
                PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(filePath)
                    .stream(inputStream, file.getSize(), -1)
                    .contentType(file.getContentType())
                    .build()
            );

            // 保存文件信息到数据库
            SysFile sysFile = new SysFile();
            sysFile.setOriginalName(originalName);
            sysFile.setFileName(fileName);
            sysFile.setFilePath(filePath);
            sysFile.setFileUrl(getFileUrl(filePath));
            sysFile.setFileSize(file.getSize());
            sysFile.setFileType(extension);
            sysFile.setMimeType(file.getContentType());
            sysFile.setFileMd5(md5);
            sysFile.setStorageType(2); // MinIO存储
            sysFile.setBucketName(minioConfig.getBucketName());
            sysFile.setIsPublic(1);
            sysFile.setCreatedTime(LocalDateTime.now());

            sysFileMapper.insert(sysFile);

            // 返回文件信息
            Map<String, Object> result = new HashMap<>();
            result.put("id", sysFile.getId());
            result.put("originalName", originalName);
            result.put("fileName", fileName);
            result.put("filePath", filePath);
            result.put("fileUrl", sysFile.getFileUrl());
            result.put("fileSize", file.getSize());
            result.put("fileType", extension);
            result.put("mimeType", file.getContentType());

            return result;

        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> uploadFiles(MultipartFile[] files) {
        List<Map<String, Object>> results = new ArrayList<>();
        for (MultipartFile file : files) {
            results.add(uploadFile(file));
        }
        return results;
    }

    @Override
    public boolean deleteFile(Long fileId) {
        try {
            SysFile sysFile = sysFileMapper.selectById(fileId);
            if (sysFile == null) {
                return false;
            }

            // 从MinIO删除文件
            minioClient.removeObject(
                RemoveObjectArgs.builder()
                    .bucket(sysFile.getBucketName())
                    .object(sysFile.getFilePath())
                    .build()
            );

            // 从数据库删除记录
            sysFileMapper.deleteById(fileId);

            return true;
        } catch (Exception e) {
            log.error("文件删除失败", e);
            return false;
        }
    }

    @Override
    public String getDownloadUrl(Long fileId) {
        try {
            SysFile sysFile = sysFileMapper.selectById(fileId);
            if (sysFile == null) {
                return null;
            }

            // 生成预签名下载URL，有效期1小时
            return minioClient.getPresignedObjectUrl(
                GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(sysFile.getBucketName())
                    .object(sysFile.getFilePath())
                    .expiry(1, TimeUnit.HOURS)
                    .build()
            );
        } catch (Exception e) {
            log.error("获取下载URL失败", e);
            return null;
        }
    }

    @Override
    public String getPreviewUrl(Long fileId) {
        return getDownloadUrl(fileId);
    }

    @Override
    public Map<String, Object> findByMd5(String md5) {
        LambdaQueryWrapper<SysFile> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysFile::getFileMd5, md5);
        SysFile sysFile = sysFileMapper.selectOne(wrapper);
        
        if (sysFile == null) {
            return null;
        }

        Map<String, Object> result = new HashMap<>();
        result.put("id", sysFile.getId());
        result.put("originalName", sysFile.getOriginalName());
        result.put("fileName", sysFile.getFileName());
        result.put("filePath", sysFile.getFilePath());
        result.put("fileUrl", sysFile.getFileUrl());
        result.put("fileSize", sysFile.getFileSize());
        result.put("fileType", sysFile.getFileType());
        result.put("mimeType", sysFile.getMimeType());

        return result;
    }

    /**
     * 获取文件访问URL
     */
    private String getFileUrl(String filePath) {
        return minioConfig.getEndpoint() + "/" + minioConfig.getBucketName() + "/" + filePath;
    }
}
