# 长沙工业学院站群管理系统

## 项目简介

长沙工业学院站群管理系统是一套基于Spring Cloud Alibaba微服务架构的现代化站群管理平台，为学校各个二级机构提供统一的信息发布和网站建设服务。

### 主要特性

- 🏗️ **微服务架构**: 基于Spring Cloud Alibaba构建，支持高并发、高可用
- 🎨 **响应式设计**: 支持PC、平板、手机多端自适应
- 👥 **多级权限管理**: 支持超级管理员和机构管理员的分级管理
- 📝 **内容管理**: 完整的CMS功能，支持文章、图片、文件管理
- 🔒 **安全可靠**: JWT认证、RBAC权限控制、数据加密
- 📊 **监控运维**: 完整的日志记录、性能监控、健康检查

## 技术架构

### 后端技术栈

- **框架**: Spring Boot 3.x + Spring Cloud Alibaba 2023.x
- **数据库**: MySQL 8.0 + Redis 7.x
- **消息队列**: RocketMQ
- **服务注册**: Nacos
- **API网关**: Spring Cloud Gateway
- **对象存储**: MinIO
- **搜索引擎**: Elasticsearch
- **容器化**: Docker + Docker Compose

### 前端技术栈

- **框架**: React 18 + TypeScript
- **构建工具**: Vite 5
- **状态管理**: Redux Toolkit
- **UI组件**: Ant Design
- **HTTP客户端**: Axios
- **样式方案**: Styled Components

## 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    前端层 (Frontend)                        │
├─────────────────────────────────────────────────────────────┤
│  React + Vite + Redux + Axios + Ant Design                 │
│  ├── 超级管理员后台                                          │
│  ├── 二级机构管理后台                                        │
│  └── 公共站点前台                                           │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    网关层 (Gateway)                         │
├─────────────────────────────────────────────────────────────┤
│  Spring Cloud Gateway + Nacos                              │
│  ├── 路由转发                                               │
│  ├── 负载均衡                                               │
│  ├── 限流熔断                                               │
│  └── 统一认证                                               │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   微服务层 (Microservices)                  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐│
│  │ 用户管理服务 │ │ 内容管理服务 │ │ 站点管理服务 │ │文件服务 ││
│  │ user-service│ │cms-service  │ │site-service │ │file-svc ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘│
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐│
│  │ 认证授权服务 │ │ 通知服务    │ │ 系统配置服务 │ │监控服务 ││
│  │ auth-service│ │notify-svc   │ │config-svc   │ │monitor  ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘│
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    数据层 (Data Layer)                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐│
│  │    MySQL    │ │    Redis    │ │   MinIO     │ │Elasticsearch││
│  │   主数据库   │ │    缓存     │ │  文件存储    │ │  搜索引擎 ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘│
└─────────────────────────────────────────────────────────────┘
```

## 快速开始

### 环境要求

- JDK 17+
- Node.js 18+
- Docker & Docker Compose
- Maven 3.8+

### 本地开发

1. **克隆项目**
```bash
git clone https://github.com/hncsut/site-group-management.git
cd site-group-management
```

2. **启动基础服务**
```bash
# 启动MySQL、Redis、MinIO、Nacos
docker-compose -f docker-compose.dev.yml up -d
```

3. **初始化数据库**
```bash
# 导入数据库脚本
mysql -h localhost -u root -p < sql/sgm_database.sql
```

4. **启动后端服务**
```bash
# 编译项目
mvn clean package -DskipTests

# 启动网关服务
cd sgm-gateway && mvn spring-boot:run

# 启动认证服务
cd sgm-auth && mvn spring-boot:run

# 启动用户服务
cd sgm-user && mvn spring-boot:run

# 启动文件服务
cd sgm-file && mvn spring-boot:run
```

5. **启动前端应用**
```bash
cd frontend
npm install
npm run dev
```

### 生产部署

1. **一键部署**
```bash
chmod +x deploy.sh
./deploy.sh
```

2. **手动部署**
```bash
# 构建后端服务
mvn clean package -DskipTests

# 构建前端应用
cd frontend && npm run build && cd ..

# 启动所有服务
docker-compose up -d
```

## 访问地址

- **前端应用**: http://localhost
- **管理后台**: http://localhost/login
- **API网关**: http://localhost:8080
- **Nacos控制台**: http://localhost:8848/nacos
- **MinIO控制台**: http://localhost:9001

## 默认账号

### 系统管理员
- 用户名: `admin`
- 密码: `123456`

### Nacos控制台
- 用户名: `nacos`
- 密码: `nacos`

### MinIO控制台
- 用户名: `minioadmin`
- 密码: `minioadmin123`

## 项目结构

```
site-group-management/
├── docs/                   # 文档目录
├── sql/                    # 数据库脚本
├── sgm-common/            # 公共模块
├── sgm-gateway/           # 网关服务
├── sgm-auth/              # 认证服务
├── sgm-user/              # 用户服务
├── sgm-cms/               # 内容管理服务
├── sgm-site/              # 站点管理服务
├── sgm-file/              # 文件服务
├── sgm-monitor/           # 监控服务
├── frontend/              # 前端应用
├── nginx/                 # Nginx配置
├── docker-compose.yml     # Docker编排文件
├── deploy.sh              # 部署脚本
└── README.md              # 项目说明
```

## 功能模块

### 超级管理员功能
- 🏢 机构管理：创建、编辑、删除二级机构
- 👥 用户管理：管理所有用户账号
- 🔐 权限管理：分配角色和权限
- 🌐 站点管理：管理所有机构站点
- 📊 系统监控：查看系统运行状态
- ⚙️ 系统配置：全局系统参数配置

### 机构管理员功能
- 🏠 站点建设：创建和管理本机构站点
- 📝 内容发布：发布文章、新闻、通知
- 📂 栏目管理：创建和管理站点栏目
- 📁 文件管理：上传和管理文件资源
- 👤 用户管理：管理本机构用户
- 📈 数据统计：查看站点访问统计

## 开发指南

### 后端开发

1. **添加新的微服务**
```bash
# 创建新模块
mkdir sgm-newservice
cd sgm-newservice

# 创建pom.xml和基本结构
# 参考现有服务结构
```

2. **数据库变更**
```sql
-- 在sql/sgm_database.sql中添加新表
CREATE TABLE new_table (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    -- 其他字段
);
```

### 前端开发

1. **添加新页面**
```typescript
// 在src/pages/目录下创建新页面
// 在src/components/Layout/index.tsx中添加路由
```

2. **添加新的API**
```typescript
// 在src/api/目录下创建API文件
export const newApi = {
  getData: () => request.get('/api/data'),
  // 其他API方法
}
```

## 部署运维

### 监控告警

系统集成了完整的监控体系：

- **应用监控**: Spring Boot Actuator
- **服务监控**: Nacos服务健康检查
- **系统监控**: Docker容器监控
- **日志监控**: 集中化日志收集

### 备份策略

1. **数据库备份**
```bash
# 每日自动备份
docker exec sgm-mysql mysqldump -u root -p sgm_db > backup_$(date +%Y%m%d).sql
```

2. **文件备份**
```bash
# 备份MinIO数据
docker cp sgm-minio:/data ./backup/minio_$(date +%Y%m%d)
```

### 性能优化

1. **数据库优化**
   - 合理设置索引
   - 定期分析慢查询
   - 配置连接池参数

2. **缓存优化**
   - Redis缓存热点数据
   - 静态资源CDN加速
   - 浏览器缓存策略

3. **服务优化**
   - JVM参数调优
   - 连接池配置
   - 异步处理优化

## 常见问题

### Q: 服务启动失败怎么办？
A: 检查日志输出，常见原因：
- 端口被占用
- 数据库连接失败
- 配置文件错误

### Q: 前端页面无法访问？
A: 检查以下几点：
- Nginx配置是否正确
- 后端服务是否正常
- 网络连接是否正常

### Q: 文件上传失败？
A: 检查：
- MinIO服务状态
- 文件大小限制
- 存储空间是否充足

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系我们

- 项目地址: https://github.com/hncsut/site-group-management
- 问题反馈: https://github.com/hncsut/site-group-management/issues
- 邮箱: <EMAIL>

---

© 2024 长沙工业学院. All rights reserved.
