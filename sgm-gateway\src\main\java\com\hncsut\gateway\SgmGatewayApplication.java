package com.hncsut.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 网关启动程序
 * 
 * <AUTHOR>
 */
@EnableDiscoveryClient
@SpringBootApplication
public class SgmGatewayApplication {
    public static void main(String[] args) {
        SpringApplication.run(SgmGatewayApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  网关启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
