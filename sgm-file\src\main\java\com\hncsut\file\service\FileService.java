package com.hncsut.file.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 文件服务接口
 * 
 * <AUTHOR>
 */
public interface FileService {

    /**
     * 上传单个文件
     *
     * @param file 文件
     * @return 文件信息
     */
    Map<String, Object> uploadFile(MultipartFile file);

    /**
     * 上传多个文件
     *
     * @param files 文件列表
     * @return 文件信息列表
     */
    List<Map<String, Object>> uploadFiles(MultipartFile[] files);

    /**
     * 删除文件
     *
     * @param fileId 文件ID
     * @return 是否成功
     */
    boolean deleteFile(Long fileId);

    /**
     * 获取文件下载URL
     *
     * @param fileId 文件ID
     * @return 下载URL
     */
    String getDownloadUrl(Long fileId);

    /**
     * 获取文件预览URL
     *
     * @param fileId 文件ID
     * @return 预览URL
     */
    String getPreviewUrl(Long fileId);

    /**
     * 根据MD5查找文件
     *
     * @param md5 文件MD5
     * @return 文件信息
     */
    Map<String, Object> findByMd5(String md5);
}
