package com.hncsut.auth.service;

import java.util.Map;

/**
 * 认证服务接口
 * 
 * <AUTHOR>
 */
public interface AuthService {

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @return 结果
     */
    Map<String, Object> login(String username, String password);

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    Map<String, Object> getInfo();

    /**
     * 退出登录
     */
    void logout();

    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 结果
     */
    Map<String, Object> refreshToken(String refreshToken);
}
