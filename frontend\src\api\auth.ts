import request from './request'

export interface LoginParams {
  username: string
  password: string
  code?: string
  uuid?: string
}

export interface LoginResponse {
  token: string
  refreshToken: string
  user: {
    id: number
    username: string
    realName: string
    email?: string
    phone?: string
    avatar?: string
    orgId: number
    userType: number
    roles: string[]
    permissions: string[]
  }
}

export const authApi = {
  // 登录
  login: (params: LoginParams) => {
    return request.post<LoginResponse>('/auth/login', params)
  },

  // 获取用户信息
  getUserInfo: () => {
    return request.get('/auth/getInfo')
  },

  // 退出登录
  logout: () => {
    return request.post('/auth/logout')
  },

  // 刷新token
  refreshToken: (refreshToken: string) => {
    return request.post('/auth/refresh', { refreshToken })
  },
}
