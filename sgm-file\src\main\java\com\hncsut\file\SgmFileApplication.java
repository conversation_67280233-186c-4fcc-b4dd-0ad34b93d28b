package com.hncsut.file;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 文件服务启动程序
 * 
 * <AUTHOR>
 */
@EnableDiscoveryClient
@SpringBootApplication
public class SgmFileApplication {
    public static void main(String[] args) {
        SpringApplication.run(SgmFileApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  文件服务启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
