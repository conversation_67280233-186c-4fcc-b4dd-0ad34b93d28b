import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { message } from 'antd'
import { store } from '@/store'
import { clearAuth, setToken } from '@/store/slices/authSlice'

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
request.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const state = store.getState()
    const token = state.auth.token

    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      }
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const { code, msg, data } = response.data

    if (code === 200) {
      return { data, msg }
    } else if (code === 401) {
      // token过期，尝试刷新token
      const state = store.getState()
      const refreshToken = state.auth.refreshToken

      if (refreshToken) {
        return refreshTokenRequest(refreshToken).then(() => {
          // 重新发送原请求
          return request(response.config)
        })
      } else {
        store.dispatch(clearAuth())
        window.location.href = '/login'
        message.error('登录已过期，请重新登录')
      }
    } else {
      message.error(msg || '请求失败')
      return Promise.reject(new Error(msg || '请求失败'))
    }
  },
  (error) => {
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          store.dispatch(clearAuth())
          window.location.href = '/login'
          message.error('登录已过期，请重新登录')
          break
        case 403:
          message.error('没有权限访问')
          break
        case 404:
          message.error('请求的资源不存在')
          break
        case 500:
          message.error('服务器内部错误')
          break
        default:
          message.error(data?.msg || '网络错误')
      }
    } else {
      message.error('网络连接失败')
    }

    return Promise.reject(error)
  }
)

// 刷新token
const refreshTokenRequest = async (refreshToken: string) => {
  try {
    const response = await axios.post('/api/auth/refresh', {
      refreshToken,
    })

    const { token, refreshToken: newRefreshToken } = response.data.data
    store.dispatch(setToken({ token, refreshToken: newRefreshToken }))

    return response
  } catch (error) {
    store.dispatch(clearAuth())
    window.location.href = '/login'
    throw error
  }
}

export default request
