# Tomcat
server:
  port: 8080

# Spring
spring:
  application:
    # 应用名称
    name: sgm-gateway
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
    gateway:
      discovery:
        locator:
          lowerCaseServiceId: true
          enabled: true
      routes:
        # 认证服务
        - id: sgm-auth
          uri: lb://sgm-auth
          predicates:
            - Path=/auth/**
          filters:
            - StripPrefix=1
        # 用户服务
        - id: sgm-user
          uri: lb://sgm-user
          predicates:
            - Path=/user/**
          filters:
            - StripPrefix=1
        # 内容管理服务
        - id: sgm-cms
          uri: lb://sgm-cms
          predicates:
            - Path=/cms/**
          filters:
            - StripPrefix=1
        # 站点管理服务
        - id: sgm-site
          uri: lb://sgm-site
          predicates:
            - Path=/site/**
          filters:
            - StripPrefix=1
        # 文件服务
        - id: sgm-file
          uri: lb://sgm-file
          predicates:
            - Path=/file/**
          filters:
            - StripPrefix=1
