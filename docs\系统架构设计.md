# 长沙工业学院站群管理系统架构设计

## 1. 整体架构

### 1.1 技术架构
```
┌─────────────────────────────────────────────────────────────┐
│                    前端层 (Frontend)                        │
├─────────────────────────────────────────────────────────────┤
│  React + Vite + Redux + Axios + Ant Design                 │
│  ├── 超级管理员后台                                          │
│  ├── 二级机构管理后台                                        │
│  └── 公共站点前台                                           │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    网关层 (Gateway)                         │
├─────────────────────────────────────────────────────────────┤
│  Spring Cloud Gateway + Nacos                              │
│  ├── 路由转发                                               │
│  ├── 负载均衡                                               │
│  ├── 限流熔断                                               │
│  └── 统一认证                                               │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   微服务层 (Microservices)                  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐│
│  │ 用户管理服务 │ │ 内容管理服务 │ │ 站点管理服务 │ │文件服务 ││
│  │ user-service│ │cms-service  │ │site-service │ │file-svc ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘│
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐│
│  │ 认证授权服务 │ │ 通知服务    │ │ 系统配置服务 │ │监控服务 ││
│  │ auth-service│ │notify-svc   │ │config-svc   │ │monitor  ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘│
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    数据层 (Data Layer)                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐│
│  │    MySQL    │ │    Redis    │ │   MinIO     │ │Elasticsearch││
│  │   主数据库   │ │    缓存     │ │  文件存储    │ │  搜索引擎 ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘│
└─────────────────────────────────────────────────────────────┘
```

### 1.2 微服务划分

#### 1.2.1 用户管理服务 (user-service)
- 用户注册、登录、注销
- 用户信息管理
- 组织机构管理
- 用户角色分配

#### 1.2.2 认证授权服务 (auth-service)
- JWT Token生成与验证
- 权限验证
- 单点登录(SSO)
- OAuth2集成

#### 1.2.3 内容管理服务 (cms-service)
- 文章管理
- 页面管理
- 栏目管理
- 内容审核

#### 1.2.4 站点管理服务 (site-service)
- 站点配置
- 模板管理
- 域名绑定
- 站点统计

#### 1.2.5 文件服务 (file-service)
- 文件上传下载
- 图片处理
- 文件权限控制
- CDN集成

## 2. 数据库设计

### 2.1 核心表结构

#### 用户相关表
```sql
-- 用户表
CREATE TABLE sys_user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    real_name VARCHAR(50),
    avatar VARCHAR(200),
    status TINYINT DEFAULT 1,
    org_id BIGINT,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 组织机构表
CREATE TABLE sys_organization (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE,
    parent_id BIGINT DEFAULT 0,
    level INT DEFAULT 1,
    sort_order INT DEFAULT 0,
    description TEXT,
    status TINYINT DEFAULT 1,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 角色表
CREATE TABLE sys_role (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    code VARCHAR(50) UNIQUE,
    description VARCHAR(200),
    status TINYINT DEFAULT 1,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 权限表
CREATE TABLE sys_permission (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    code VARCHAR(50) UNIQUE,
    type TINYINT COMMENT '1:菜单 2:按钮 3:接口',
    parent_id BIGINT DEFAULT 0,
    path VARCHAR(200),
    component VARCHAR(200),
    icon VARCHAR(50),
    sort_order INT DEFAULT 0,
    status TINYINT DEFAULT 1
);
```

#### 站点相关表
```sql
-- 站点表
CREATE TABLE cms_site (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    domain VARCHAR(100),
    template_id BIGINT,
    org_id BIGINT,
    logo VARCHAR(200),
    favicon VARCHAR(200),
    keywords VARCHAR(500),
    description TEXT,
    status TINYINT DEFAULT 1,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 栏目表
CREATE TABLE cms_category (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL,
    parent_id BIGINT DEFAULT 0,
    path VARCHAR(200),
    template VARCHAR(100),
    sort_order INT DEFAULT 0,
    status TINYINT DEFAULT 1,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 内容表
CREATE TABLE cms_content (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    site_id BIGINT NOT NULL,
    category_id BIGINT NOT NULL,
    title VARCHAR(200) NOT NULL,
    content LONGTEXT,
    summary TEXT,
    cover_image VARCHAR(200),
    author VARCHAR(50),
    source VARCHAR(100),
    tags VARCHAR(500),
    view_count INT DEFAULT 0,
    status TINYINT DEFAULT 1,
    publish_time DATETIME,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 3. 技术选型说明

### 3.1 后端技术栈
- **Spring Cloud Alibaba 2023.x**: 微服务框架
- **Spring Boot 3.x**: 应用框架
- **Nacos**: 服务注册发现、配置中心
- **Sentinel**: 流量控制、熔断降级
- **Seata**: 分布式事务
- **MySQL 8.0**: 关系型数据库
- **Redis 7.x**: 缓存数据库
- **MinIO**: 对象存储
- **Elasticsearch**: 搜索引擎

### 3.2 前端技术栈
- **React 18**: 前端框架
- **Vite 5**: 构建工具
- **Redux Toolkit**: 状态管理
- **Axios**: HTTP客户端
- **Ant Design**: UI组件库
- **React Router**: 路由管理
- **TypeScript**: 类型检查

### 3.3 部署技术栈
- **Docker**: 容器化
- **Kubernetes**: 容器编排
- **Nginx**: 反向代理
- **Jenkins**: CI/CD
- **Prometheus**: 监控
- **Grafana**: 可视化
