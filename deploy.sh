#!/bin/bash

# 长沙工业学院站群管理系统部署脚本
# 作者: HNCSUT
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_docker() {
    log_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "Docker环境检查通过"
}

# 检查Java和Maven
check_java_maven() {
    log_info "检查Java和Maven环境..."
    
    if ! command -v java &> /dev/null; then
        log_error "Java未安装，请先安装JDK 17"
        exit 1
    fi
    
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装，请先安装Maven"
        exit 1
    fi
    
    log_success "Java和Maven环境检查通过"
}

# 检查Node.js和npm
check_node() {
    log_info "检查Node.js环境..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装，请先安装Node.js 18+"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm未安装，请先安装npm"
        exit 1
    fi
    
    log_success "Node.js环境检查通过"
}

# 构建后端服务
build_backend() {
    log_info "开始构建后端服务..."
    
    # 清理并编译
    mvn clean package -DskipTests
    
    log_success "后端服务构建完成"
}

# 构建前端应用
build_frontend() {
    log_info "开始构建前端应用..."
    
    cd frontend
    
    # 安装依赖
    npm install
    
    # 构建应用
    npm run build
    
    cd ..
    
    log_success "前端应用构建完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p nginx/ssl
    mkdir -p logs
    mkdir -p data/mysql
    mkdir -p data/redis
    mkdir -p data/minio
    
    log_success "目录创建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 停止现有服务
    docker-compose down
    
    # 启动服务
    docker-compose up -d
    
    log_success "服务启动完成"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    sleep 30
    
    # 检查各个服务
    services=("sgm-mysql" "sgm-redis" "sgm-minio" "sgm-nacos" "sgm-gateway" "sgm-auth" "sgm-user" "sgm-file" "sgm-frontend" "sgm-nginx")
    
    for service in "${services[@]}"; do
        if docker ps | grep -q $service; then
            log_success "$service 运行正常"
        else
            log_error "$service 启动失败"
            docker logs $service
        fi
    done
}

# 显示访问信息
show_access_info() {
    log_info "部署完成！访问信息如下："
    echo ""
    echo "🌐 前端应用: http://localhost"
    echo "🔧 管理后台: http://localhost/login"
    echo "📊 Nacos控制台: http://localhost:8848/nacos"
    echo "📁 MinIO控制台: http://localhost:9001"
    echo ""
    echo "默认登录账号:"
    echo "用户名: admin"
    echo "密码: 123456"
    echo ""
    echo "Nacos控制台:"
    echo "用户名: nacos"
    echo "密码: nacos"
    echo ""
    echo "MinIO控制台:"
    echo "用户名: minioadmin"
    echo "密码: minioadmin123"
}

# 主函数
main() {
    log_info "开始部署长沙工业学院站群管理系统..."
    
    # 环境检查
    check_docker
    check_java_maven
    check_node
    
    # 创建目录
    create_directories
    
    # 构建应用
    build_backend
    build_frontend
    
    # 启动服务
    start_services
    
    # 检查服务状态
    check_services
    
    # 显示访问信息
    show_access_info
    
    log_success "部署完成！"
}

# 帮助信息
show_help() {
    echo "长沙工业学院站群管理系统部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -s, --stop     停止所有服务"
    echo "  -r, --restart  重启所有服务"
    echo "  -l, --logs     查看服务日志"
    echo ""
}

# 停止服务
stop_services() {
    log_info "停止所有服务..."
    docker-compose down
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启所有服务..."
    docker-compose restart
    log_success "服务已重启"
}

# 查看日志
show_logs() {
    docker-compose logs -f
}

# 参数处理
case "$1" in
    -h|--help)
        show_help
        ;;
    -s|--stop)
        stop_services
        ;;
    -r|--restart)
        restart_services
        ;;
    -l|--logs)
        show_logs
        ;;
    *)
        main
        ;;
esac
