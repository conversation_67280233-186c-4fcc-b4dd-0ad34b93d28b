-- 长沙工业学院站群管理系统数据库设计
-- 创建数据库
CREATE DATABASE IF NOT EXISTS sgm_db DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE sgm_db;

-- ========================================
-- 用户管理相关表
-- ========================================

-- 组织机构表
CREATE TABLE sys_organization (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    name VARCHAR(100) NOT NULL COMMENT '机构名称',
    code VARCHAR(50) UNIQUE COMMENT '机构编码',
    parent_id BIGINT DEFAULT 0 COMMENT '父机构ID',
    level INT DEFAULT 1 COMMENT '机构层级',
    sort_order INT DEFAULT 0 COMMENT '排序',
    description TEXT COMMENT '描述',
    contact_person VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    address VARCHAR(200) COMMENT '地址',
    status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建者',
    updated_by VARCHAR(50) COMMENT '更新者',
    deleted TINYINT DEFAULT 0 COMMENT '删除标志(0:存在 1:删除)',
    remark TEXT COMMENT '备注'
) COMMENT '组织机构表';

-- 用户表
CREATE TABLE sys_user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    real_name VARCHAR(50) COMMENT '真实姓名',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar VARCHAR(200) COMMENT '头像',
    gender TINYINT DEFAULT 0 COMMENT '性别(0:未知 1:男 2:女)',
    birthday DATE COMMENT '生日',
    org_id BIGINT COMMENT '所属机构ID',
    user_type TINYINT DEFAULT 1 COMMENT '用户类型(1:超级管理员 2:机构管理员 3:普通用户)',
    status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    login_ip VARCHAR(50) COMMENT '最后登录IP',
    login_time DATETIME COMMENT '最后登录时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建者',
    updated_by VARCHAR(50) COMMENT '更新者',
    deleted TINYINT DEFAULT 0 COMMENT '删除标志(0:存在 1:删除)',
    remark TEXT COMMENT '备注',
    INDEX idx_username (username),
    INDEX idx_org_id (org_id),
    INDEX idx_status (status)
) COMMENT '用户表';

-- 角色表
CREATE TABLE sys_role (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    name VARCHAR(50) NOT NULL COMMENT '角色名称',
    code VARCHAR(50) UNIQUE COMMENT '角色编码',
    description VARCHAR(200) COMMENT '角色描述',
    data_scope TINYINT DEFAULT 1 COMMENT '数据范围(1:全部 2:本机构及以下 3:本机构 4:仅本人)',
    status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建者',
    updated_by VARCHAR(50) COMMENT '更新者',
    deleted TINYINT DEFAULT 0 COMMENT '删除标志(0:存在 1:删除)',
    remark TEXT COMMENT '备注'
) COMMENT '角色表';

-- 权限表
CREATE TABLE sys_permission (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    name VARCHAR(50) NOT NULL COMMENT '权限名称',
    code VARCHAR(50) UNIQUE COMMENT '权限编码',
    type TINYINT COMMENT '权限类型(1:菜单 2:按钮 3:接口)',
    parent_id BIGINT DEFAULT 0 COMMENT '父权限ID',
    path VARCHAR(200) COMMENT '路由路径',
    component VARCHAR(200) COMMENT '组件路径',
    icon VARCHAR(50) COMMENT '图标',
    sort_order INT DEFAULT 0 COMMENT '排序',
    visible TINYINT DEFAULT 1 COMMENT '是否显示(0:隐藏 1:显示)',
    status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建者',
    updated_by VARCHAR(50) COMMENT '更新者',
    deleted TINYINT DEFAULT 0 COMMENT '删除标志(0:存在 1:删除)',
    remark TEXT COMMENT '备注'
) COMMENT '权限表';

-- 用户角色关联表
CREATE TABLE sys_user_role (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    created_by VARCHAR(50) COMMENT '创建者',
    UNIQUE KEY uk_user_role (user_id, role_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id)
) COMMENT '用户角色关联表';

-- 角色权限关联表
CREATE TABLE sys_role_permission (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    permission_id BIGINT NOT NULL COMMENT '权限ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    created_by VARCHAR(50) COMMENT '创建者',
    UNIQUE KEY uk_role_permission (role_id, permission_id),
    INDEX idx_role_id (role_id),
    INDEX idx_permission_id (permission_id)
) COMMENT '角色权限关联表';

-- ========================================
-- 站点管理相关表
-- ========================================

-- 站点表
CREATE TABLE cms_site (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    name VARCHAR(100) NOT NULL COMMENT '站点名称',
    domain VARCHAR(100) COMMENT '域名',
    template_id BIGINT COMMENT '模板ID',
    org_id BIGINT COMMENT '所属机构ID',
    logo VARCHAR(200) COMMENT 'Logo',
    favicon VARCHAR(200) COMMENT '网站图标',
    title VARCHAR(200) COMMENT '网站标题',
    keywords VARCHAR(500) COMMENT '关键词',
    description TEXT COMMENT '网站描述',
    copyright VARCHAR(200) COMMENT '版权信息',
    icp_record VARCHAR(100) COMMENT 'ICP备案号',
    statistics_code TEXT COMMENT '统计代码',
    custom_head TEXT COMMENT '自定义头部代码',
    custom_footer TEXT COMMENT '自定义底部代码',
    status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建者',
    updated_by VARCHAR(50) COMMENT '更新者',
    deleted TINYINT DEFAULT 0 COMMENT '删除标志(0:存在 1:删除)',
    remark TEXT COMMENT '备注',
    INDEX idx_org_id (org_id),
    INDEX idx_domain (domain)
) COMMENT '站点表';

-- 模板表
CREATE TABLE cms_template (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    name VARCHAR(100) NOT NULL COMMENT '模板名称',
    code VARCHAR(50) UNIQUE COMMENT '模板编码',
    type TINYINT DEFAULT 1 COMMENT '模板类型(1:PC 2:移动端 3:响应式)',
    preview_image VARCHAR(200) COMMENT '预览图',
    template_path VARCHAR(200) COMMENT '模板路径',
    config_json TEXT COMMENT '配置JSON',
    is_default TINYINT DEFAULT 0 COMMENT '是否默认模板',
    status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建者',
    updated_by VARCHAR(50) COMMENT '更新者',
    deleted TINYINT DEFAULT 0 COMMENT '删除标志(0:存在 1:删除)',
    remark TEXT COMMENT '备注'
) COMMENT '模板表';

-- 栏目表
CREATE TABLE cms_category (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    site_id BIGINT NOT NULL COMMENT '站点ID',
    name VARCHAR(100) NOT NULL COMMENT '栏目名称',
    parent_id BIGINT DEFAULT 0 COMMENT '父栏目ID',
    path VARCHAR(200) COMMENT '栏目路径',
    template VARCHAR(100) COMMENT '栏目模板',
    list_template VARCHAR(100) COMMENT '列表模板',
    detail_template VARCHAR(100) COMMENT '详情模板',
    image VARCHAR(200) COMMENT '栏目图片',
    description TEXT COMMENT '栏目描述',
    keywords VARCHAR(500) COMMENT '关键词',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_nav TINYINT DEFAULT 1 COMMENT '是否导航显示',
    target VARCHAR(20) DEFAULT '_self' COMMENT '打开方式',
    link_url VARCHAR(500) COMMENT '外链地址',
    status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建者',
    updated_by VARCHAR(50) COMMENT '更新者',
    deleted TINYINT DEFAULT 0 COMMENT '删除标志(0:存在 1:删除)',
    remark TEXT COMMENT '备注',
    INDEX idx_site_id (site_id),
    INDEX idx_parent_id (parent_id)
) COMMENT '栏目表';

-- 内容表
CREATE TABLE cms_content (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    site_id BIGINT NOT NULL COMMENT '站点ID',
    category_id BIGINT NOT NULL COMMENT '栏目ID',
    title VARCHAR(200) NOT NULL COMMENT '标题',
    subtitle VARCHAR(200) COMMENT '副标题',
    content LONGTEXT COMMENT '内容',
    summary TEXT COMMENT '摘要',
    cover_image VARCHAR(200) COMMENT '封面图',
    images TEXT COMMENT '图片集合(JSON)',
    attachments TEXT COMMENT '附件集合(JSON)',
    author VARCHAR(50) COMMENT '作者',
    source VARCHAR(100) COMMENT '来源',
    source_url VARCHAR(500) COMMENT '来源链接',
    tags VARCHAR(500) COMMENT '标签',
    seo_title VARCHAR(200) COMMENT 'SEO标题',
    seo_keywords VARCHAR(500) COMMENT 'SEO关键词',
    seo_description TEXT COMMENT 'SEO描述',
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    like_count INT DEFAULT 0 COMMENT '点赞次数',
    comment_count INT DEFAULT 0 COMMENT '评论次数',
    is_top TINYINT DEFAULT 0 COMMENT '是否置顶',
    is_recommend TINYINT DEFAULT 0 COMMENT '是否推荐',
    is_hot TINYINT DEFAULT 0 COMMENT '是否热门',
    content_type TINYINT DEFAULT 1 COMMENT '内容类型(1:文章 2:图片 3:视频 4:下载)',
    status TINYINT DEFAULT 1 COMMENT '状态(0:草稿 1:已发布 2:已下线)',
    publish_time DATETIME COMMENT '发布时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建者',
    updated_by VARCHAR(50) COMMENT '更新者',
    deleted TINYINT DEFAULT 0 COMMENT '删除标志(0:存在 1:删除)',
    remark TEXT COMMENT '备注',
    INDEX idx_site_category (site_id, category_id),
    INDEX idx_status (status),
    INDEX idx_publish_time (publish_time),
    INDEX idx_is_top (is_top),
    INDEX idx_is_recommend (is_recommend)
) COMMENT '内容表';

-- ========================================
-- 文件管理相关表
-- ========================================

-- 文件表
CREATE TABLE sys_file (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    original_name VARCHAR(200) NOT NULL COMMENT '原始文件名',
    file_name VARCHAR(200) NOT NULL COMMENT '存储文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_url VARCHAR(500) COMMENT '访问URL',
    file_size BIGINT COMMENT '文件大小(字节)',
    file_type VARCHAR(50) COMMENT '文件类型',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    file_md5 VARCHAR(32) COMMENT '文件MD5',
    storage_type TINYINT DEFAULT 1 COMMENT '存储类型(1:本地 2:MinIO 3:阿里云OSS)',
    bucket_name VARCHAR(100) COMMENT '存储桶名称',
    is_public TINYINT DEFAULT 1 COMMENT '是否公开(0:私有 1:公开)',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建者',
    updated_by VARCHAR(50) COMMENT '更新者',
    deleted TINYINT DEFAULT 0 COMMENT '删除标志(0:存在 1:删除)',
    remark TEXT COMMENT '备注',
    INDEX idx_file_md5 (file_md5),
    INDEX idx_file_type (file_type),
    INDEX idx_created_by (created_by)
) COMMENT '文件表';

-- ========================================
-- 系统配置相关表
-- ========================================

-- 系统配置表
CREATE TABLE sys_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    config_name VARCHAR(100) NOT NULL COMMENT '配置名称',
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键名',
    config_value TEXT COMMENT '配置键值',
    config_type TINYINT DEFAULT 1 COMMENT '配置类型(1:系统配置 2:用户配置)',
    is_system TINYINT DEFAULT 0 COMMENT '是否系统内置(0:否 1:是)',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建者',
    updated_by VARCHAR(50) COMMENT '更新者',
    deleted TINYINT DEFAULT 0 COMMENT '删除标志(0:存在 1:删除)',
    remark TEXT COMMENT '备注'
) COMMENT '系统配置表';

-- 操作日志表
CREATE TABLE sys_operation_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    title VARCHAR(50) COMMENT '模块标题',
    business_type TINYINT DEFAULT 0 COMMENT '业务类型(0:其它 1:新增 2:修改 3:删除)',
    method VARCHAR(100) COMMENT '方法名称',
    request_method VARCHAR(10) COMMENT '请求方式',
    operator_type TINYINT DEFAULT 0 COMMENT '操作类别(0:其它 1:后台用户 2:手机端用户)',
    operator_name VARCHAR(50) COMMENT '操作人员',
    operator_url VARCHAR(255) COMMENT '请求URL',
    operator_ip VARCHAR(50) COMMENT '主机地址',
    operator_location VARCHAR(255) COMMENT '操作地点',
    operator_param TEXT COMMENT '请求参数',
    json_result TEXT COMMENT '返回参数',
    status TINYINT DEFAULT 0 COMMENT '操作状态(0:正常 1:异常)',
    error_msg VARCHAR(2000) COMMENT '错误消息',
    operation_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    INDEX idx_operator_name (operator_name),
    INDEX idx_operation_time (operation_time)
) COMMENT '操作日志表';

-- ========================================
-- 初始化数据
-- ========================================

-- 插入默认组织机构
INSERT INTO sys_organization (id, name, code, parent_id, level, sort_order, description, status) VALUES
(1, '长沙工业学院', 'HNCSUT', 0, 1, 1, '长沙工业学院', 1),
(2, '党委办公室', 'DWBGS', 1, 2, 1, '党委办公室', 1),
(3, '校长办公室', 'XZBGS', 1, 2, 2, '校长办公室', 1),
(4, '教务处', 'JWC', 1, 2, 3, '教务处', 1),
(5, '学生工作处', 'XSGZC', 1, 2, 4, '学生工作处', 1),
(6, '机械工程学院', 'JXGCXY', 1, 2, 5, '机械工程学院', 1),
(7, '电气信息学院', 'DQXXXY', 1, 2, 6, '电气信息学院', 1),
(8, '计算机学院', 'JSJXY', 1, 2, 7, '计算机学院', 1);

-- 插入默认角色
INSERT INTO sys_role (id, name, code, description, data_scope, status) VALUES
(1, '超级管理员', 'SUPER_ADMIN', '超级管理员，拥有所有权限', 1, 1),
(2, '机构管理员', 'ORG_ADMIN', '机构管理员，管理本机构站点', 2, 1),
(3, '内容编辑', 'CONTENT_EDITOR', '内容编辑，负责内容发布', 3, 1);

-- 插入默认用户
INSERT INTO sys_user (id, username, password, real_name, org_id, user_type, status) VALUES
(1, 'admin', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMqGYyTZqw/8fLaiKiQP8XpuOPiOm', '超级管理员', 1, 1, 1);

-- 插入用户角色关联
INSERT INTO sys_user_role (user_id, role_id) VALUES (1, 1);

-- 插入默认权限
INSERT INTO sys_permission (id, name, code, type, parent_id, path, component, icon, sort_order, visible, status) VALUES
(1, '系统管理', 'system', 1, 0, '/system', NULL, 'system', 1, 1, 1),
(2, '用户管理', 'system:user', 1, 1, '/system/user', 'system/user/index', 'user', 1, 1, 1),
(3, '角色管理', 'system:role', 1, 1, '/system/role', 'system/role/index', 'peoples', 2, 1, 1),
(4, '权限管理', 'system:permission', 1, 1, '/system/permission', 'system/permission/index', 'tree-table', 3, 1, 1),
(5, '机构管理', 'system:org', 1, 1, '/system/org', 'system/org/index', 'tree', 4, 1, 1),
(6, '站点管理', 'cms', 1, 0, '/cms', NULL, 'website', 2, 1, 1),
(7, '站点配置', 'cms:site', 1, 6, '/cms/site', 'cms/site/index', 'component', 1, 1, 1),
(8, '栏目管理', 'cms:category', 1, 6, '/cms/category', 'cms/category/index', 'tree-table', 2, 1, 1),
(9, '内容管理', 'cms:content', 1, 6, '/cms/content', 'cms/content/index', 'documentation', 3, 1, 1),
(10, '模板管理', 'cms:template', 1, 6, '/cms/template', 'cms/template/index', 'theme', 4, 1, 1);

-- 插入角色权限关联
INSERT INTO sys_role_permission (role_id, permission_id) VALUES
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5), (1, 6), (1, 7), (1, 8), (1, 9), (1, 10),
(2, 6), (2, 7), (2, 8), (2, 9), (2, 10),
(3, 6), (3, 8), (3, 9);

-- 插入默认模板
INSERT INTO cms_template (id, name, code, type, preview_image, template_path, is_default, status) VALUES
(1, '默认PC模板', 'default_pc', 1, '/templates/default_pc/preview.jpg', '/templates/default_pc', 1, 1),
(2, '响应式模板', 'responsive', 3, '/templates/responsive/preview.jpg', '/templates/responsive', 0, 1);

-- 插入默认站点
INSERT INTO cms_site (id, name, domain, template_id, org_id, title, keywords, description, status) VALUES
(1, '长沙工业学院官网', 'www.hncsut.edu.cn', 1, 1, '长沙工业学院', '长沙工业学院,高等教育,工科院校', '长沙工业学院官方网站', 1);

-- 插入系统配置
INSERT INTO sys_config (config_name, config_key, config_value, config_type, is_system) VALUES
('系统名称', 'sys.name', '长沙工业学院站群管理系统', 1, 1),
('系统版本', 'sys.version', '1.0.0', 1, 1),
('文件上传路径', 'sys.upload.path', '/upload', 1, 1),
('文件上传大小限制', 'sys.upload.maxSize', '10485760', 1, 1),
('允许上传的文件类型', 'sys.upload.allowTypes', 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,zip,rar', 1, 1);
