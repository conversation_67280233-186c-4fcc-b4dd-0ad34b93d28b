import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { message } from 'antd'
import { authApi } from '@/api/auth'

export interface User {
  id: number
  username: string
  realName: string
  email?: string
  phone?: string
  avatar?: string
  orgId: number
  userType: number
  roles: string[]
  permissions: string[]
}

interface AuthState {
  token: string | null
  refreshToken: string | null
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
}

const initialState: AuthState = {
  token: localStorage.getItem('token'),
  refreshToken: localStorage.getItem('refreshToken'),
  user: null,
  isLoading: false,
  isAuthenticated: !!localStorage.getItem('token'),
}

// 异步登录
export const login = createAsyncThunk(
  'auth/login',
  async (credentials: { username: string; password: string }) => {
    const response = await authApi.login(credentials)
    return response.data
  }
)

// 获取用户信息
export const getUserInfo = createAsyncThunk('auth/getUserInfo', async () => {
  const response = await authApi.getUserInfo()
  return response.data
})

// 退出登录
export const logout = createAsyncThunk('auth/logout', async () => {
  await authApi.logout()
})

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearAuth: (state) => {
      state.token = null
      state.refreshToken = null
      state.user = null
      state.isAuthenticated = false
      localStorage.removeItem('token')
      localStorage.removeItem('refreshToken')
    },
    setToken: (state, action: PayloadAction<{ token: string; refreshToken: string }>) => {
      state.token = action.payload.token
      state.refreshToken = action.payload.refreshToken
      state.isAuthenticated = true
      localStorage.setItem('token', action.payload.token)
      localStorage.setItem('refreshToken', action.payload.refreshToken)
    },
  },
  extraReducers: (builder) => {
    builder
      // 登录
      .addCase(login.pending, (state) => {
        state.isLoading = true
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false
        state.token = action.payload.token
        state.refreshToken = action.payload.refreshToken
        state.user = action.payload.user
        state.isAuthenticated = true
        localStorage.setItem('token', action.payload.token)
        localStorage.setItem('refreshToken', action.payload.refreshToken)
        message.success('登录成功')
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false
        message.error(action.error.message || '登录失败')
      })
      // 获取用户信息
      .addCase(getUserInfo.fulfilled, (state, action) => {
        state.user = action.payload
      })
      // 退出登录
      .addCase(logout.fulfilled, (state) => {
        state.token = null
        state.refreshToken = null
        state.user = null
        state.isAuthenticated = false
        localStorage.removeItem('token')
        localStorage.removeItem('refreshToken')
        message.success('退出成功')
      })
  },
})

export const { clearAuth, setToken } = authSlice.actions
export default authSlice.reducer
