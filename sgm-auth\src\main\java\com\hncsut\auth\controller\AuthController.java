package com.hncsut.auth.controller;

import com.hncsut.auth.domain.dto.LoginDto;
import com.hncsut.auth.service.AuthService;
import com.hncsut.common.core.domain.R;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 认证授权控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {

    private final AuthService authService;

    /**
     * 登录方法
     *
     * @param loginDto 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public R<Map<String, Object>> login(@Validated @RequestBody LoginDto loginDto) {
        Map<String, Object> result = authService.login(loginDto.getUsername(), loginDto.getPassword());
        return R.ok(result);
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/getInfo")
    public R<Map<String, Object>> getInfo() {
        Map<String, Object> userInfo = authService.getInfo();
        return R.ok(userInfo);
    }

    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public R<Void> logout() {
        authService.logout();
        return R.ok();
    }

    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 结果
     */
    @PostMapping("/refresh")
    public R<Map<String, Object>> refresh(@RequestParam String refreshToken) {
        Map<String, Object> result = authService.refreshToken(refreshToken);
        return R.ok(result);
    }
}
