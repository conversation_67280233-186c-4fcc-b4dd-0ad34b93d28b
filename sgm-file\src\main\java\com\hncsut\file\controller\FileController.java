package com.hncsut.file.controller;

import com.hncsut.common.core.domain.R;
import com.hncsut.file.service.FileService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 文件控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/file")
@RequiredArgsConstructor
public class FileController {

    private final FileService fileService;

    /**
     * 上传单个文件
     */
    @PostMapping("/upload")
    public R<Map<String, Object>> uploadFile(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return R.fail("请选择要上传的文件");
        }
        
        Map<String, Object> result = fileService.uploadFile(file);
        return R.ok(result, "文件上传成功");
    }

    /**
     * 上传多个文件
     */
    @PostMapping("/upload/batch")
    public R<List<Map<String, Object>>> uploadFiles(@RequestParam("files") MultipartFile[] files) {
        if (files == null || files.length == 0) {
            return R.fail("请选择要上传的文件");
        }
        
        List<Map<String, Object>> results = fileService.uploadFiles(files);
        return R.ok(results, "文件上传成功");
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/{fileId}")
    public R<Void> deleteFile(@PathVariable Long fileId) {
        boolean success = fileService.deleteFile(fileId);
        return success ? R.ok("文件删除成功") : R.fail("文件删除失败");
    }

    /**
     * 获取文件下载URL
     */
    @GetMapping("/download/{fileId}")
    public R<String> getDownloadUrl(@PathVariable Long fileId) {
        String url = fileService.getDownloadUrl(fileId);
        return url != null ? R.ok(url) : R.fail("文件不存在");
    }

    /**
     * 获取文件预览URL
     */
    @GetMapping("/preview/{fileId}")
    public R<String> getPreviewUrl(@PathVariable Long fileId) {
        String url = fileService.getPreviewUrl(fileId);
        return url != null ? R.ok(url) : R.fail("文件不存在");
    }

    /**
     * 根据MD5查找文件
     */
    @GetMapping("/md5/{md5}")
    public R<Map<String, Object>> findByMd5(@PathVariable String md5) {
        Map<String, Object> result = fileService.findByMd5(md5);
        return result != null ? R.ok(result) : R.fail("文件不存在");
    }
}
